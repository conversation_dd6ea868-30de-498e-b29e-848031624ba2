// 股票行情API服务
function StockApiService() {}

StockApiService.getMarketId = function(stockCode) {
    return stockCode.startsWith('6') ? 1 : 0;
};

StockApiService.buildSecid = function(stockCode) {
    const marketId = this.getMarketId(stockCode);
    return `${marketId}.${stockCode}`;
};

StockApiService.getStockRealtime = async function(stockCode) {
    const secid = this.buildSecid(stockCode);
    const url = `https://push2.eastmoney.com/api/qt/stock/get?secid=${secid}`;
    
    try {
        const response = await fetch(url);
        const result = await response.json();
        if (result.rc === 0 && result.data) {
            return result.data;
        }
        throw new Error('获取数据失败');
    } catch (error) {
        console.error('获取股票数据失败:', error);
        return null;
    }
};

// 获取股票列表数据
StockApiService.getStockList = async function(stockCodes) {
    const promises = stockCodes.map(function(code) {
        return this.getStockRealtime(code);
    }, this);
    return await Promise.all(promises);
};

module.exports = StockApiService; 