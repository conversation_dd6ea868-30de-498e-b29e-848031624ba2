// 通知服务
class NotificationService {
  static async sendWxMessage(title, msg) {
    const token = localStorage.getItem('xtuisToken');
    if (!token) {
      console.warn('未设置虾推啥Token，无法发送消息');
      return;
    }

    const url = `https://wx.xtuis.cn/${token}.send?text=${encodeURIComponent(title)}&desp=${encodeURIComponent(msg)}`;
    try {
      const response = await fetch(url);
      const data = await response.json();
      if (data.success) {
        console.log('消息发送成功');
      } else {
        console.error('消息发送失败:', data);
      }
    } catch (error) {
      console.error('发送消息时出错:', error);
    }
  }

  // 股票相关通知
  static async notifyStockBreak(code, name) {
    const title = '股票炸板提醒';
    const msg = `股票 ${code} ${name} 已炸板`;
    await this.sendWxMessage(title, msg);
  }

  static async notifyBuyVolumeDecrease(code, name, decreasePercent) {
    const title = '封单减少提醒';
    const msg = `股票 ${code} ${name} 封单减少 ${decreasePercent}%`;
    await this.sendWxMessage(title, msg);
  }

  static async notifyBuyVolumeChange(code, name, changeVolume) {
    const title = '封单变化提醒';
    const msg = `股票 ${code} ${name} 封单变化 ${changeVolume > 0 ? '+' : ''}${changeVolume}`;
    await this.sendWxMessage(title, msg);
  }
}

module.exports = NotificationService; 