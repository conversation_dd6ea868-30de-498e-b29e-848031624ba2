const loki = require('lokijs');
const path = require('path');
const TdxService = require('./tdxService.js');
const fs = require('fs');

class DailyReviewService {
    constructor() {
        this.db = null;
        this.dailyReviews = null;
        this.stockNames = new Map(); // 缓存股票名称
        this.init();
        this.dbFolder=path.join(__dirname, '../../database');
    }

    async init() {
        return new Promise((resolve, reject) => {
            try {
                // 确保数据库目录存在
                const dbDir = path.join(__dirname, '../../database');
                if (!fs.existsSync(dbDir)) {
                    fs.mkdirSync(dbDir, { recursive: true });
                }

                // 使用绝对路径存储数据库文件
                const dbPath = path.join(dbDir, 'dailyreview.db');
                console.log('数据库文件路径:', dbPath);

                this.db = new loki(dbPath, {
                    autoload: true,
                    autoloadCallback: () => {
                        console.log('数据库加载完成');
                        this.dailyReviews = this.db.getCollection('daily_reviews');
                        if (!this.dailyReviews) {
                            console.log('创建daily_reviews集合');
                            this.dailyReviews = this.db.addCollection('daily_reviews', {
                                indices: ['date'],
                                unique: ['date']
                            });
                        } else {
                            console.log('找到daily_reviews集合，当前记录数:', this.dailyReviews.count());
                        }
                        resolve();
                    },
                    autosave: true,
                    autosaveInterval: 5000
                });
            } catch (error) {
                console.error('初始化数据库失败:', error);
                reject(error);
            }
        });
    }
    getMa(stock, index, days)
    {
        var ma=0;
        for(var v=index; v>0; v--){
            var md=new stock.get(v);
            ma+=md.close;
            if( (index-v)==(days-1)) {
                return ma/days;
            }
        }
        return 0;//没有足够的天数
    }

    
    saveObjectToFile(obj, filePath) {
        try {
            fs.writeFileSync(filePath, JSON.stringify(obj), 'utf8');
        } catch (error) {
            console.error('Error saving file:', error);
        }
    }
    tdx_fupan(from, to, alldays)
    {

        //var alldays={};

        var stockIndex=new TdxService();
        stockIndex.tdxDays('399001');  //加载日线数据
        
        var codes=stockIndex.getAllStockCodes();
        console.log('total stocks: '+codes.length);

        for(var ci=0; ci<codes.length; ci++) {
            var code=codes[ci];
            
            //var stockid=ori_stockid.substr(1);	//通达信自定义板块会在代码前加0和1，分别代表深圳和上海
            //stockid='300505';
            var stock=new TdxService();
            stock.tdxDays(code);  //加载日线数据
            if( stock.length==0 ) continue;
            
           
            var endIndex= stock.getDayIndex(to);
            endIndex=endIndex>=stock.length ?(stock.length-1):endIndex;
            for(var k=endIndex; k>0; k--){
                var td= stock.get(k);
                var pred= stock.get(k-1);
                if( td.day>=from && td.day<=to ){
                    var index=td.day.toString();
                    if( (!(index in alldays)) || alldays[index].ok==1){
                        alldays[index]={};
                        //alldays[index].day=td.day;
                        //alldays[index].tradevalue=0;	//用连板数、涨停数、开板率、跌停数、涨停股票次日表现来计算，
                        //alldays[index].lb=0;	//连板数
                        alldays[index].zt=0;	//涨停数量
                        alldays[index].kb=0;	//开板数量
                        //alldays[index].ztrate=0;	//开板率 kbcount/(kbcount+ztcount)
                        alldays[index].dt=0;	//跌停
                        alldays[index].dtkb=0;	//跌停开板数
                        
                        alldays[index].yzb=0;	//一字板
                        
                        //alldays[index].zxtc=0;	//主线题材赚钱效应，也即大资金。将成交额大于15亿的涨幅相加。
                        
                        //alldays[index].crcount=0;	//涨停+开板股数量
                        //alldays[index].zth=0;	//涨停股次日表现，最高涨幅
                        //alldays[index].ztc=0;	//涨停股次日平均收盘涨幅		
                        alldays[index].zts=[];	//所有涨停股
                        alldays[index].zt1=0;   //首板涨停数量

                        alldays[index].kbs=[];	//开板股
                        alldays[index].dts=[];	//跌停股
                        
                        alldays[index].qsups=[];	//趋势股，连涨长时间排序
                        alldays[index].qsdowns=[];	//趋势股，连跌最长时间排序

                        alldays[index].bdups=[];	//波段股，20交易日波段涨幅排序
                        alldays[index].bddowns=[];	//波段股，波段跌幅最大排序
                        
                       
                        alldays[index].kpups=[];	//最强势高开股
                        alldays[index].kpdowns=[];	//低开股

                        alldays[index].spups=[];	//收盘涨幅前20
                        alldays[index].spdowns=[];	//收盘跌幅前20

                        alldays[index].tas=[];	//两市成交额前20
                        
                        alldays[index].fzups=[];	//反转股，日内上涨最多前20
                        alldays[index].fzdowns=[];	//反转股，日内下跌最多前20
                        
                        alldays[index].closeups=[];	//日内上涨最多前20
                        alldays[index].closedowns=[];//日内下跌最多前20
                        
                        alldays[index].aths=0;  //alltimehigh 历史新高
                        alldays[index].atls=0;  //alltimelow 历史新低
                        

                        alldays[index].ok=0;					
                    }
                                                         
                                        
                    ///////////////////////////////////////////////////////////////////
                    //是否高开股。记录市场最强龙头高开幅度，以便得知市场是处于加速段，还是分歧段。选择3天内至少有一天涨停且成交额大于2亿的
                    //2018-06-14，改为统计市场所有个股高开前列的，借以研究竞价高开模式是赚是亏，如果是盈利的，表面市场较好
                    //2019-03-14 改为近5个交易日有过涨停的个股的高开幅度
                    if( k>0 ){
                            
                        var zf=100*(td.open/pred.close-1);							
                        
                        //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                        var gks={c:code,zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amount/100000000).toFixed(2))};
                        var inserted=false;
                        for(var li=0; li<alldays[index].kpups.length; li++){
                            //开盘涨幅越大越靠前
                            if( zf>alldays[index].kpups[li].zf ){
                                alldays[index].kpups.splice(li,0,gks);
                                inserted=true;									
                                break;
                            }	
                        }
                        if(!inserted && alldays[index].kpups.length<30){
                            alldays[index].kpups.push(gks);
                        }
                    }
                    
                    ///////////////////////////////////////////////////////////////////
                    //是否低开股。当出现大量大幅低开股时，说明市场风险很大
                    //去掉除权除息
                    if(k>0){
                        
                             zf=100*(td.open/pred.close-1);							
                        //保存低开股
                            var dks={c:code,zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amount/100000000).toFixed(2))};
                            inserted=false;
                            for( li=0; li<alldays[index].kpdowns.length; li++){
                                //开盘跌幅越大越靠前
                                if( zf<alldays[index].kpdowns[li].zf ){
                                    alldays[index].kpdowns.splice(li,0,dks);
                                    inserted=true;
                                    break;
                                }	
                            }
                            if(!inserted && alldays[index].kpdowns.length<30){
                                alldays[index].kpdowns.push(dks);
                            }
                    }
                
                    ///////////////////////////////////////////////////////////////////
                    //反转上涨股。从最低点到收盘，涨幅大于9%以上的，按照顺序排列
                    if( k>0 ){
                            
                        zf=100*(td.close/td.low-1);					
                        
                        //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                        var fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amount/100000000).toFixed(2))};
                        inserted=false;
                        for( li=0; li<alldays[index].fzups.length; li++){
                            //开盘涨幅越大越靠前
                            if( zf>alldays[index].fzups[li].zf ){
                                alldays[index].fzups.splice(li,0,fzs);
                                inserted=true;									
                                break;
                            }	
                        }
                        if(!inserted && zf>0 && alldays[index].fzups.length<30){
                            alldays[index].fzups.push(fzs);
                        }
                    }

                    ///////////////////////////////////////////////////////////////////
                    //反转下跌股。从最高点到收盘，跌幅大于9%以上的，按照顺序排列
                    if( k>0 ){
                            
                        zf=100*(td.close/td.high-1);			
                        
                        //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                        fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amount/100000000).toFixed(2))};
                        inserted=false;
                        for( li=0; li<alldays[index].fzdowns.length; li++){
                            //开盘涨幅越大越靠前
                            if( zf<0 && zf<alldays[index].fzdowns[li].zf ){
                                alldays[index].fzdowns.splice(li,0,fzs);
                                inserted=true;									
                                break;
                            }	
                        }
                        if(!inserted && zf<0 && alldays[index].fzdowns.length<30){
                            alldays[index].fzdowns.push(fzs);
                        }
                    }
                    
                    ///////////////////////////////////////////////////////////////////
                    //收盘上涨股。按照顺序排列
                    if( k>0 ){
                            
                        zf=100*(td.close/pred.close-1);			
                        
                        //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                        fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amount/100000000).toFixed(2))};
                        inserted=false;
                        for( li=0; li<alldays[index].closeups.length; li++){
                            //涨幅越大越靠前
                            if( zf>alldays[index].closeups[li].zf ){
                                alldays[index].closeups.splice(li,0,fzs);
                                inserted=true;									
                                break;
                            }	
                        }
                        if(!inserted && zf>0 && alldays[index].closeups.length<30){
                            alldays[index].closeups.push(fzs);
                        }
                    }

                    ///////////////////////////////////////////////////////////////////
                    //收盘下跌股。按照顺序排列
                    if( k>0 ){
                            
                        zf=100*(td.close/td.high-1);		

                        //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                        fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amount/100000000).toFixed(2))};
                        inserted=false;
                        for( li=0; li<alldays[index].closedowns.length; li++){
                            //开盘涨幅越大越靠前
                            if( zf<0 && zf<alldays[index].closedowns[li].zf ){
                                alldays[index].closedowns.splice(li,0,fzs);
                                inserted=true;									
                                break;
                            }	
                        }
                        if(!inserted && zf<0 && alldays[index].closedowns.length<30){
                            alldays[index].closedowns.push(fzs);
                        }
                    }

                    ///////////////////////////////////////////////////////////////////
                    //是否涨停股
                    if( stock.isRiseLimit(td.close,pred.close) ){	//是否涨停
                        var lbcount=0;
                        var isyzb=(td.close==td.low);// && td.amount<50000000);	//首板是否无量一字板，成交额小于5000万
                       
                        for(var v=k; v>0; v--){
                            var td2=stock.get(v);
                            var pred2=stock.get(v-1);
                            if( stock.isRiseLimit(td2.close,pred2.close) ){                                
                                lbcount++;
                            }else{
                                break;
                            }
                        }
                        
                        alldays[index].zt+=lbcount>0?1:0;
                        alldays[index].yzb+=isyzb?1:0;
                        
                        //涨停股:股票代码，连板数，成交额
                        var lts={c:code,lb:lbcount,v:parseFloat((td.amount/100000000).toFixed(2))};
                        if(lbcount>0){
                            inserted=false;
                            for(li=0; li<alldays[index].zts.length; li++){
                                //连板越高越靠前，同等板数，成交额越大越靠前
                                if( (lbcount>alldays[index].zts[li].lb) || (lbcount==alldays[index].zts[li].lb && lts.v>alldays[index].zts[li].v) ){
                                    alldays[index].zts.splice(li,0,lts);
                                    inserted=true;									
                                    break;
                                }	
                            }
                                                        
                            if(!inserted && alldays[index].zts.length<30){
                                alldays[index].zts.push(lts);
                            }
                        }
                    }


                    ///////////////////////////////////////////////////////////////////
                   //是否跌停股
                    if( stock.isFallLimit(td.close,pred.close) ){	//是否跌停
                                                
                        alldays[index].dt++;
                    }
                    ///////////////////////////////////////////////////////////////////
                   //是否开板股
                   if( td.close<td.high && stock.isRiseLimit(td.high,pred.close) ){	//是否涨停
                                                
                        alldays[index].kb++;
                    }
                    
                    
                    //最大成交额
                    if( k>0 ){
                            
                        zf=100*(td.close/pred.close-1);		
                        var cv=td.amount/100000000;
                        //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                        fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat(cv.toFixed(2))};
                        inserted=false;
                        for( li=0; li<alldays[index].tas.length; li++){
                            //开盘涨幅越大越靠前
                            if(  cv>alldays[index].tas[li].v ){
                                alldays[index].tas.splice(li,0,fzs);
                                inserted=true;									
                                break;
                            }	
                        }
                        if(!inserted && alldays[index].tas.length<30){
                            alldays[index].tas.push(fzs);
                        }
                    }

                }else if(td.day<from ){
                    break;
                }

                alldays[index].kpups.splice(30);
                alldays[index].kpdowns.splice(30);
                alldays[index].fzups.splice(30);
                alldays[index].fzdowns.splice(30);
                alldays[index].closeups.splice(30);
                alldays[index].closedowns.splice(30);
                alldays[index].zts.splice(30);
                alldays[index].tas.splice(30);

            }//end for days
        }//end for codes
        

        for( index in alldays){
            if( alldays[index].ok==0 ){
                alldays[index].ok=1;
            }
        }

        //将复盘结果写入文件，如果每天写入的文字过多，或者复盘的时间太长，可以压缩或者按年分割，压缩可能好一点
        this.saveObjectToFile(alldays, this.dbFolder+'/dr.db' );
        
        
    }


}

module.exports = new DailyReviewService(); 