// 通达信数据服务
const fs = require('fs');
const iconv = require('iconv-lite');

// 添加Date原型扩展
Date.prototype.addDays = function(days) {
    var date = new Date(this.valueOf());
    date.setDate(date.getDate() + days);
    return date;
};

Date.prototype.Format = function(fmt) {
    var o = {
        'M+': this.getMonth() + 1,
        'd+': this.getDate(),
        'h+': this.getHours(),
        'm+': this.getMinutes(),
        's+': this.getSeconds(),
        'q+': Math.floor((this.getMonth() + 3) / 3),
        'S': this.getMilliseconds()
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp('(' + k + ')').test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
    return fmt;
};

// 辅助函数
function readTextFromFile(filePath) {
    try {
        return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
        console.error('Error reading file:', error);
        return '';
    }
}


class TdxService{


  constructor() {
      this.code = ''; // 股票代码
      this.length=0;//日线长度
      this.tdxFolder=localStorage.getItem('tdxFolder') || 'D:/new_tdx';
      this.items = {}; // 存储股票信息的对象
      this.codes = []; // 存储股票代码的数组
  }

  //找到最近的交易日
  getLastTradeDay(formarter)
  {
    var today = new Date();
    if(today.getDay()==0) today.addDays(-2);
    if(today.getDay()==6) today.addDays(-1);
    
    formarter=formarter||'yyyyMMdd';
    var tradeday= today.Format(formarter) ;
    return tradeday;
  }
  //找到前一个交易日
  getPreTradeDay(formarter)
  {
    var today = new Date();
    today.addDays(-1);
    if(today.getDay()==0) today.addDays(-2);
    if(today.getDay()==6) today.addDays(-1);
    
    formarter=formarter||'yyyyMMdd';
    var tradeday=today.Format(formarter) ;
    return tradeday;
  }	

  //将通达信的day转为date
  dayToDate(tdxDay)
  {
    var pattern = /(\d{4})(\d{2})(\d{2})/;
    return new Date(tdxDay.toString().replace(pattern, '$1/$2/$3'));
  }
  /*
  代码段	      市场板块	        所属前缀	示例代码	说明
  000000–000999	深市主板	        sz	    sz000001	如平安银行
  001000–001999	深市主板（新段）	  sz	    sz001979	新代码段，属深市主板
  002000–002999	深市中小板	      sz	    sz002415	如海康威视
  003000–003999	深市中小板（新）	sz	    sz003816	稀有，算中小板延伸
  300000–399999	深市创业板	      sz	    sz300750	如宁德时代
  600000–603999	沪市主板	        sh    	sh600519	如贵州茅台
  605000–605999	沪市主板（新）	  sh	    sh605111	沪主板扩容
  688000–689999	沪市科创板	      sh	    sh688001	如华兴源创
  430000–439999	北交所（原新三板）	bj  	bj430047	北交所股票
  800000–899999	北交所（新段）	    bj  	bj830946	新三板转板企业，北交所新段
  700000–799999	备用段/军工配股等	——	——	暂无主板企业使用
  */

  //是否涨停,价格单位元，浮点数。tofixed有精度损耗问题，有时会出错
  isZT(close,preclose)
  {
    if(preclose==0) return false;
    preclose=Math.round(preclose*100);
    var zt=Math.round(preclose*1.1);
    var c=Math.round(close*100);
    return c==zt;
  }

  getZDF(code)
  {
    var sc=code.substr(0,2);
    if( sc=='00'|| sc=='60'){
      return 0.1;
    }else if( sc=='43'|| sc=='83'|| sc=='87'){
      return 0.3;
    }else if( sc=='30'|| sc=='68'){
      return 0.2;
    }
    return 0.1;
  }

  //是否涨停,11,10 价格单位分，如19.98=1998，添加上市天数，判断是否是新股前5日
  isRiseLimit(close,preclose)
  {
    if(preclose==0 || this.code=='' || this.length<=5) return false;
    var zdf=this.getZDF(this.code);
    var zt=Math.round(preclose*(1+zdf));
      
    return Math.round(close)==zt;
  }

  //是否跌停,9,10
  isFallLimit(close,preclose)
  {
    if(preclose==0 || this.code=='' || this.length<=5) return false;
    var zdf=this.getZDF(this.code);
    var zt=Math.round(preclose*(1-zdf));
    
    return Math.round(close)==zt;
  }

  codeToTdxSymbol(code)
  {
    var stockid=code;
    if( code.substr(0,1)=='0' || code.substr(0,1)=='3' ){
      stockid='0'+code;
    }else if(code.substr(0,1)=='6'||code.substr(0,1)=='8'||code.substr(0,1)=='9'){
      stockid='1'+code;
    }
    
    return stockid;	
  }


  //得到已交易时间
  GetTradeTime()
  {
    var cd=new Date();
    var hours=cd.getHours();
    var minutes=cd.getMinutes();
    var dt=parseInt(cd.Format('hhmm'));
    var dif=0;
    if(  dt>930 && dt<1300 ) {
      if( dt>1130 ){
        hours=11;
        minutes=30;
      }
      dif=(hours-9)*60+minutes-30;
    }else if(dt>=1300 && dt<=1500){
      dif=120+(hours-13)*60+minutes;
    }else{
      dif=240;
    }
    return dif;
  }

  //通达信日线数据，为32字节：Vday/Vopen/Vhigh/Vlow/Vclose/famount/Vvolume/Vpreclose
  //buf为node.js的Buffer
  tdxDays(stockid){
    var dayLength=32;	//每个日线数据为32字节
    var dayfile='';
    var sc=stockid.substr(0,1);
    if( sc=='6' || sc=='9'  || sc=='8'){
      dayfile=this.tdxFolder+'/vipdoc/sh/lday/sh'+stockid+'.day';
    }else if( sc=='3' || sc=='0'){
      dayfile=this.tdxFolder+'/vipdoc/sz/lday/sz'+stockid+'.day';
    }else if( sc=='4' || sc=='8'){
      dayfile=this.tdxFolder+'/vipdoc/bj/lday/bj'+stockid+'.day';
    }
    
    var data=null;
    this.length=0;
    this.code=stockid;
    //mylog("process:"+dayfile);
    if( fs.existsSync(dayfile) ) {
      data=fs.readFileSync(dayfile);	
      this.length=data.length/dayLength;
    }
    
    this.get = function(index) {
      var intLength=4;
      var item={};
      item.day=data.readUInt32LE(index*32+intLength*0);
      item.open=data.readUInt32LE(index*32+intLength*1);
      item.high=data.readUInt32LE(index*32+intLength*2);
      item.low=data.readUInt32LE(index*32+intLength*3);
      item.close=data.readUInt32LE(index*32+intLength*4);
      item.amount=data.readFloatLE(index*32+intLength*5);
      item.volume=data.readUInt32LE(index*32+intLength*6);
      item.preclose=data.readUInt32LE(index*32+intLength*7);
      //由于原始数据的preclose不对，应读取前一日的收盘价
      if(index>0) item.preclose=data.readUInt32LE( (index-1)*32+intLength*4);
      return item;
    };
  }

  tdxDay(day)
  {
    var dindex=this.getDayIndex(this, day);
    if(dindex>=0 && dindex<this.length ){
      var td=new this.get(dindex);
      return td;
    }
    return {};
  }

  //二分法
  getDayIndex(day)
  {
    var l=1, h=this.length-1;
    while( l<=h ){
      var m=parseInt((l+h)/2);
      var td=new this.get(m);
      if( day==td.day ) return m;
      else if( day<td.day ) h=m-1;
      else l=m+1;
    }
    return this.length;
  }

  //通达信分钟线数据，为32字节
  /*
  每32个字节为一个5分钟数据，每字段内低字节在前
      00 ~ 01 字节：日期，整型，设其值为num，则日期计算方法为：
              year=floor(num/2048)+2004;
              month=floor(mod(num,2048)/100);
              day=mod(mod(num,2048),100);
      02 ~ 03 字节： 从0点开始至目前的分钟数，整型
      04 ~ 07 字节：开盘价（分），float型
      08 ~ 11 字节：最高价（分），float型
      12 ~ 15 字节：最低价（分），float型
      16 ~ 19 字节：收盘价（分），float型
      20 ~ 23 字节：成交额（元），float型
      24 ~ 27 字节：成交量（股），整型
      28 ~ 31 字节：（保留）
  */
  //buf为node.js的Buffer
  tdxMinutes(stockid){
    var dayLength=32;	//每个分钟线数据为32字节
    var dayfile='';
    if( stockid.substr(0,1)=='6'){
      dayfile=this.tdxFolder+'/vipdoc/sh/minline/sh'+stockid+'.lc1';
    }else if( stockid.substr(0,1)=='3' || stockid.substr(0,1)=='0'){
      dayfile=this.tdxFolder+'/vipdoc/sz/minline/sz'+stockid+'.lc1';
    }
    
    var _this=this;
    this.data=null;
    this.length=0;
    //mylog("process:"+dayfile);
    if( fs.existsSync(dayfile) ) {
      this.data=fs.readFileSync(dayfile);	
      this.length=this.data.length/dayLength;
    }
    
    this.get = function(index) {
      var intLength=4;
      var rt={};
      
      var dnum=_this.data.readUInt16LE(index*32);
      var year=Math.floor(dnum/2048)+2004;
        var month=Math.floor((dnum%2048)/100);
        var day=(dnum%2048)%100;
      rt.day=year.toString()+('0'+month).substr(-2)+('0'+day).substr(-2);
          
      rt.startMinutes=_this.data.readUInt16LE(index*32+16);
      rt.open=_this.data.readFloatLE(index*32+intLength*1);
      rt.high=_this.data.readFloatLE(index*32+intLength*2);
      rt.low=_this.data.readFloatLE(index*32+intLength*3);
      rt.close=_this.data.readFloatLE(index*32+intLength*4);
      rt.amount=_this.data.readFloatLE(index*32+intLength*5);
      rt.volume=parseInt(_this.data.readUInt32LE(index*32+intLength*6)/100);
      rt.preclose=_this.data.readUInt32LE(index*32+intLength*7);
      
      return rt;		
    };
  }

  //二分法找到某日，然后读取所有分钟线
  getDayMinutesIndex(fh, stockid, day)
  {
    var minLength=32;	//每个分钟线数据为32字节
    
    try{
      var dlen=fs.fstatSync(fh).size/(minLength*240);	//日线个数
    }catch(e){
      console.log(e.name+e.message);
      return -1;
    }
    
    var l=1, h=dlen-1;
    var buf=new Buffer(minLength);
    while( l<=h ){
      var m=parseInt((l+h)/2);
      
      fs.readSync(fh,buf,0,minLength, m*240*minLength);
      
      var dnum=buf.readUInt16LE(0);
      var year=Math.floor(dnum/2048)+2004;
        var month=Math.floor((dnum%2048)/100);
        var _day=(dnum%2048)%100;
      
      var cday=year.toString()+('0'+month).substr(-2)+('0'+_day).substr(-2);
      if(cday==day) {
        return m;
      }
      
      else if( day<cday ) h=m-1;
      else l=m+1;
    }
    
    return -1;
    
  }

  //某日的分钟线stock=600356 day=20161010   
  //每分钟32字节，1天是32*240=7680字节，3000只股票是22500KB=22MB
  tdxDayMinutes(stockid,day){
    var _this=this;
    this.get = function(index) {
      var intLength=4;
      
      var dnum=_this.data.readUInt16LE(index*32);
      var year=Math.floor(dnum/2048)+2004;
        var month=Math.floor((dnum%2048)/100);
        var day0=(dnum%2048)%100;
      
      var rt={};
      rt.day=year.toString()+('0'+month).substr(-2)+('0'+day0).substr(-2);
      
      rt.startMinutes=_this.data.readUInt16LE(index*32+16);
      rt.open=_this.data.readFloatLE(index*32+intLength*1);
      rt.high=_this.data.readFloatLE(index*32+intLength*2);
      rt.low=_this.data.readFloatLE(index*32+intLength*3);
      rt.close=_this.data.readFloatLE(index*32+intLength*4);
      rt.amount=_this.data.readFloatLE(index*32+intLength*5);
      rt.volume=parseInt(_this.data.readUInt32LE(index*32+intLength*6)/100);
      rt.preclose=_this.data.readUInt32LE(index*32+intLength*7);
      
      return rt;
    };
    
    var minLength=32;	//每个分钟线数据为32字节
    var dayfile='';
    if( stockid.substr(0,1)=='6' || stockid.substr(0,1)=='9' || stockid.substr(0,1)=='8'){
      dayfile=this.tdxFolder+'/vipdoc/sh/minline/sh'+stockid+'.lc1';
    }else if( stockid.substr(0,1)=='3' || stockid.substr(0,1)=='0'){
      dayfile=this.tdxFolder+'/vipdoc/sz/minline/sz'+stockid+'.lc1';
    }
    
    this.code=stockid;
    this.data=null;
    this.length=0;
    this.preclose=0;
    this.preclose2=0;
    //mylog("process:"+dayfile);
    if( fs.existsSync(dayfile) ) {
      var	fh=fs.openSync(dayfile,'r');
      var cIndex=this.getDayMinutesIndex(fh, stockid,day);
      if( cIndex!=-1 ){
        var dlen=(240+1)*minLength;
        var buf=new Buffer( dlen );	//	多读取一天，取出前一天的收盘价
        fs.readSync(fh,buf,0, dlen, (cIndex*240-1)*minLength);
        fs.closeSync(fh);
        
        this.data=buf;
        var prem=this.get(0);
        this.preclose=prem.close;
        
        this.data=buf.slice(minLength);	
        this.length=240;	

        //得到当日的开盘价
        var _m=this.get(0);
        this.open=_m.open;
        
        return;
      }
      
      fs.closeSync(fh);
        
      /*
      this.data=fs.readFileSync(dayfile);	
      this.length=this.data.length/dayLength;
      
      for(var i=0; i<this.length; i+=240)
      {
        var m= this.get(i);
        if( m.day==day ) {
          if(i==0 || (i+240)>this.length ) break;	//如果是第一日，无法取前一日的收盘价，所以终止
          
          var prem= this.get(i-1);
          this.preclose=prem.close;
          
          this.preclose2=0;
          if( (i-240-1)>=0 ){
            var prem2=this.get(i-240-1);
            this.preclose2=prem2.close;
          }
          
          var buf=new Buffer(240*32);
          this.data.copy(buf, 0, i*32, (i+240)*32 );
          this.data=buf;
          this.length=240;
          
          return;
        }
      }
      */
      
      this.data=null;
      this.length=0;		
    }

  }

  addUniqueElement(arr,ele)
  {
    var added=false;
    for( var k in arr)
    {
      if(arr[k]==ele){
        added=true;
        break;
      }
    }
    if(!added) arr.push(ele);
  }

  //得到通达信所有股票的代码
  getAllStockCodes()
  {
      var fcodes=[];
    var tdxblockfile=this.tdxFolder+'/T0002/hq_cache/base.map';
    if( fs.existsSync(tdxblockfile)){
      var alldaysstr=readTextFromFile(tdxblockfile);
      var codes=alldaysstr.split('\r\n');
      for( var i=0; i<codes.length; i++){
        var c=codes[i].trim();
        var c1=c.substr(0,2);
        if(c.length==11 && (c1=='00'||c1=='30'||c1=='60'||c1=='68'||c1=='43'||c1=='83'||c1=='87')){
          fcodes.push(c.substr(0,6));
        }
      }
      //if(fcodes.length>1000) this.codes=fcodes;		
    }
    return fcodes;
  }

  //得到通达信所有股票的代码和名称
  getAllStocks()
  {
    var fcodes=[];
    
    //上海股
    var tdxblockfile=this.tdxFolder+'/T0002/hq_cache/shm.tnf';
    if( fs.existsSync(tdxblockfile)){
    
      var	fh=fs.openSync(tdxblockfile,'r');
      var flen=fs.fstatSync(fh).size;
      var count=(flen-50)/314;
      var buf=new Buffer(64);	
      for( var i=0; i<count; i++){
        fs.readSync(fh,buf,0, 32, (i*314+50));	//取代码+名称 共32字节
        
        var code=buf.toString('ascii',0,6);
        var buf2=buf.slice(23,31);
        var name=iconv.decode(buf2,'gb2312');
        
        var c1=code.substr(0,2);
        if(c1=='00'||c1=='30'||c1=='60'){
          this.addUniqueElement(fcodes,code);
          this.items[code]={n:name};
        }
      }
      fs.closeSync(fh);
    }

    //深圳股
    tdxblockfile=this.tdxFolder+'/T0002/hq_cache/szm.tnf';
    if( fs.existsSync(tdxblockfile)){
    
       fh=fs.openSync(tdxblockfile,'r');
       flen=fs.fstatSync(fh).size;
       count=(flen-50)/314;
       buf=new Buffer(64);	
      for( i=0; i<count; i++){
        fs.readSync(fh,buf,0, 32, (i*314+50));	//取代码+名称 共32字节
        
         code=buf.toString('ascii',0,6);
         buf2=buf.slice(23,31);
         name=iconv.decode(buf2,'gb2312');
        
         c1=code.substr(0,2);
        if(c1=='00'||c1=='30'||c1=='60'){
          this.addUniqueElement(fcodes,code);
          this.items[code]={n:name};
        }
      }
      fs.closeSync(fh);
    }
    
    if(fcodes.length>1000) this.codes=fcodes;
  }


}

module.exports = TdxService;
