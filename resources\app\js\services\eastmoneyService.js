const fs = require('fs');
const path = require('path');
const zlib = require('zlib');
const sleep=(ms)=>{return new Promise(resolve=>setTimeout(resolve,ms));};

function saveObjectToFile(obj, fname) {
    try {
        const jsonStr = JSON.stringify(obj);
        const compressed = zlib.gzipSync(jsonStr);
        fs.writeFileSync(fname, compressed);
        console.log(`✅ 已保存：${fname}`);
    }catch(error) {
        console.error('Error saving file:', error);
    }
}
function loadObjectFromFile(fname) {
    try {
        const compressed = fs.readFileSync(fname);
        const jsonStr = zlib.gunzipSync(compressed).toString('utf-8');
        return JSON.parse(jsonStr);
    }catch(error) {
        console.error('Error loading file:', error);
        return {};
    }
}

class eastmoneyService{
    //测试接口
    constructor() {
        this.dbFolder=path.join(__dirname, '../../database');
        this.allStocks=loadObjectFromFile( this.dbFolder+'/allstocks.json');
        this.listapi_url='.push2.eastmoney.com/api/qt/clist/get?pn=1&pz=100&po=1&fid=f3&fs=m:0+t:6,m:0+t:13,m:0+t:80,m:1+t:2,m:1+t:23&fields=f2,f5,f6,f12,f14,f15,f16,f17,f18,f20,f21';
        this.klineapi_url='.push2his.eastmoney.com/api/qt/stock/kline/get?secid=1.600519&fields1=f1&fields2=f51,f52,f53,f54,f55,f56,f57 &klt=101&fqt=1&beg=20250501&end=20250523';
        this.currentServerId=1;
        this.totalServerCount=88;
        this.currentPageId=1;

        this.totalPages = 58; // 设置总请求次数 共5719只，每页100只
        this.currentRequests = 0; // 当前已请求次数
        this.alldays=loadObjectFromFile(this.dbFolder+'/dailyreview.json' );

        this.stockIndex= this.getStockIndex();
    }

    //获取当日行情
    async fetchJsonData(baseUrl) {
        try {
            const headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Accept': '*/*',
                'Referer': 'https://quote.eastmoney.com/',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Connection': 'keep-alive',
              };
            const response = await fetch(baseUrl, { headers });
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('获取数据失败:', error);
            return null;
        }
    }

    //5719只
    async getAllStocks() {
        this.allStocks={};
        const t1=new Date().getTime();
        for(var i=1; i<=this.totalPages; i++){
            var data=null;
            for(var k=0; k<8; k++){
                var url='http://'+this.currentServerId+'.push2.eastmoney.com/api/qt/clist/get?pn='+i+'&pz=100&po=1&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048&fields=f2,f5,f6,f12,f14,f15,f16,f17,f18,f20,f21';
                data=await this.fetchJsonData(url);
                this.currentServerId++;
                if(this.currentServerId>this.totalServerCount) this.currentServerId=1;
                if(data!=null) break;
                    
                //如果获取失败，则尝试新的server
            }
            if(data!=null && data.data!=null){
                console.log('第'+i+'页，total '+data.data.total+', serverId '+this.currentServerId);
                for(let idx in data.data.diff){
                    var it=data.data.diff[idx];
                   // this.allStocks[it.f12]={n:it.f14, vol:it.f5, amo:it.f6, c:it.f2, h:it.f15, l:it.f16, o:it.f17, prec:it.f18, sz:it.f20, ltsz:it.f21};
                    this.allStocks[it.f12]=[it.f14, it.f5, Math.round(it.f6/1000000), it.f2, it.f15, it.f16, it.f17, it.f18, Math.round(it.f20/1000000), Math.round(it.f21/1000000)];//成交额、市值单位为百万
                }
            }else{
                console.log('第'+i+'页，数据获取失败! ');
            }
            await sleep(100);
        }
        const t2=new Date().getTime()-t1;
        const len=Object.keys(this.allStocks).length;
        console.log('数据获取完成，共 '+len+'只股票， 耗时: '+t2);
        //save file
        saveObjectToFile(this.allStocks,this.dbFolder+'/allstocks.json');

        //自动保存到日线
        await this.smartUpdateAllStocksKLine();
    }

    //更聪明的更新所有k线。只更新当日数据，如果超过2日，可能涉及到除权除息问题，所以不能单独更新一段时间，而应该全部重新下载
    async smartUpdateAllStocksKLine(){
        //先获取上证指数
        var stockIndex=await this.getStockIndex();
        if(stockIndex==null || !Array.isArray(stockIndex)) {
            return;
        }
        var fsiname=this.dbFolder+'/shindex.json';
        //saveObjectToFile(stockIndex, fsiname);
        //return;

        //与本地比对
        var localIndex=loadObjectFromFile(fsiname);
        if( !Array.isArray(localIndex) || localIndex.length<100) {
            console.log('加载本地指数文件失败');
            return;
        }
        //已经更新过，两者相同
        if( stockIndex[stockIndex.length-1]==localIndex[localIndex.length-1]) {
            console.log('已经更新过');
            return;
        }
        if( stockIndex[stockIndex.length-2]!=localIndex[localIndex.length-1]){
            console.log('无法更新，超过一天，可全量更新：'+localIndex[localIndex.length-1]);
            return;
        } 
        //更新所有文件
        var updateCount=0;
        for(let code in this.allStocks){
            var std=this.allStocks[code];
            if(std[1]==0 || std[3]==0) continue;    //如果成交量或价格为0，则返回，可能是停牌或其它原因

            var stock=this.loadStock(code);  //加载日线数据
            if( !Array.isArray(stock) || stock.length==0 ) continue;
            var tday=stockIndex[stockIndex.length-1].substring(0, stockIndex[stockIndex.length-1].indexOf(','));
            //allstock结构{0 name, 1 vol, 2 amo, 3 c, 4 h, 5 l, 6 o, 7 prec, sz, ltsz}//价格单位是分，成交额与市值单位是百万
            tday=tday+','+(std[6]/100).toFixed(2)+','+(std[3]/100).toFixed(2)+','+(std[4]/100).toFixed(2)+','+(std[5]/100).toFixed(2)+','+std[1]+','+std[2];
            stock.push(tday);

            //保存
            const fname=this.dbFolder+'/d'+code.substring(0,1)+'/'+code+'.json';
            saveObjectToFile(stock, fname);
            console.log('已更新股票：'+code);
            updateCount++;
        }

        console.log('更新完成，共更新股票：'+updateCount);
        if(updateCount>4000){
            //保存本地index
            saveObjectToFile(stockIndex, fsiname);
        }
    }

    //下载所有股票最近一年的日线数据 ["日2024-01-02,开19.92,收20.88,高21.32,低19.91,量271286,额570902166.00","2024-01-03,20.76,20.93,21.08,20.62,147028,307957587.00"]
    async updateAllStocksKLine(){
        const codes=Object.keys(this.allStocks);
        const t1=new Date().getTime();
        console.log('开始下载日线，股票总数:'+codes.length);
        var today=new Date().Format('yyyyMMdd');
        for(var i=0; i<codes.length; i++){
            
            var code = codes[i];
            var data=null;
            var secid=(code.startsWith('6')?'1.':'0.')+code;
            var fname=this.dbFolder+'/d'+code.substring(0,1)+'/'+code+'.json';
            if(fs.existsSync(fname)) continue;  //文件存在则跳过

            await sleep(100);   //防止请求太过于密集导致封ip

            for(var k=0; k<8; k++){
                const startday='20240101';

                var url='http://'+this.currentServerId+'.push2his.eastmoney.com/api/qt/stock/kline/get?secid='+secid+'&fields1=f1&fields2=f51,f52,f53,f54,f55,f56,f57&klt=101&fqt=1&beg='+startday+'&end='+today;
                //console.log(url);
                data=await this.fetchJsonData(url);
                this.changeServerId();
                if(data!=null) break;
                    
                //如果获取失败，则尝试新的server
            }
            if(data!=null && data.data!=null){
                console.log('第'+i+'只股票：'+code+', serverId '+this.currentServerId);
                
                saveObjectToFile(data.data.klines, fname);
            }else{
                console.log('第'+i+'只股票：'+code+'，数据获取失败! ');
                break;
            }
        }
        const t2=new Date().getTime()-t1;
        console.log('数据获取完成，共 '+codes.length+'只股票， 耗时: '+t2);
    }

    async downloadKLine(code){
        const klines=await this.getkline(code);
        var fname=this.dbFolder+'/d'+code.substring(0,1)+'/'+code+'.json';
        saveObjectToFile(klines, fname);
        await sleep(100);   //防止请求太过于密集导致封ip
    }

    changeServerId(){
        this.currentServerId++;
        if(this.currentServerId>this.totalServerCount) this.currentServerId=1;
    }
    async getkline(code){
        this.changeServerId();
        var secid=(code.startsWith('6')?'1.':'0.')+code;
        if(code=='sh000001') secid='1.000001';
        var today=new Date().Format('yyyyMMdd');
        var url='http://'+this.currentServerId+'.push2his.eastmoney.com/api/qt/stock/kline/get?secid='+secid+'&fields1=f1&fields2=f51,f52,f53,f54,f55,f56,f57&klt=101&fqt=1&beg=20240101&end='+today;
        return await this.fetchJsonData(url);
    }

    loadStock(code){
        var fname=this.dbFolder+'/d'+code.substring(0,1)+'/'+code+'.json';
        return loadObjectFromFile(fname);
    }

    //day: 2025-05-25
    findday(stock,day){
        const d1=new Date(day).getTime();
        for(var i=(stock.length-1); i>0; i--){
            if( stock[i].startsWith(day)) return i;
            const d2=new Date(stock[i].substring(0, stock[i].indexOf(','))).getTime();
            if (d2<d1) return -1;
        }
        return -1;
    }
    getZDF(code){
      var sc=code.substr(0,2);
      if( sc=='00'|| sc=='60'){
        return 0.1;
      }else if( sc=='43'|| sc=='83'|| sc=='87'){
        return 0.3;
      }else if( sc=='30'|| sc=='68'){
        return 0.2;
      }
      return 0.1;
    }
  
    //是否涨停,11,10 价格单位分，如19.98=1998，添加上市天数，判断是否是新股前5日
    isRiseLimit(code, close,preclose) {
      if(preclose==0 || code=='' ) return false;
      var zdf=this.getZDF(code);
      var zt=(preclose*(1+zdf)).toFixed(2);
        
      return close.toFixed(2)==zt;
    }
  
    //是否跌停,9,10
    isFallLimit(code,close,preclose){
      if(preclose==0 || code=='' ) return false;
      var zdf=this.getZDF(code);
      var zt=(preclose*(1-zdf)).toFixed(2);
      
      return close.toFixed(2)==zt;
    }

    async getStockIndex(){
        if(this.stockIndex!=null) return this.stockIndex;
        var stockIndex=await this.getkline('sh000001');
        if(stockIndex==null || stockIndex.data==null) {
            console.log('上证指数获取失败');
            return null;
        }
        this.stockIndex=stockIndex.data.klines;
        return this.stockIndex;
    }
    //日线数据 ["日2024-01-02,开19.92,收20.88,高21.32,低19.91,量271286,额570902166.00","2024-01-03,20.76,20.93,21.08,20.62,147028,307957587.00"]
    gettd(td){
        return {day:td[0], open:parseFloat(td[1]),close:parseFloat(td[2]),high:parseFloat(td[3]),low:parseFloat(td[4]),vol:parseInt(td[5]),amo:parseInt(td[6]) };
    }
    async daily_fupan()
    {
        this.currentServerId=66;
        //先获取上证指数
        var stockIndex=await this.getStockIndex();
        if(stockIndex==null || !Array.isArray(stockIndex)) {
            return;
        }
        
        var codes=Object.keys(this.allStocks);
        console.log('total stocks: '+codes.length);
        const st1=new Date().getTime();

        //有多少天没有复盘过了
        let count=0;
        for(let x=0;x<stockIndex.length;x++){
            let c=stockIndex[x].substring(0, stockIndex[x].indexOf(','));
            if( !Object.hasOwn(this.alldays,c)) count++;
        }
        //多加2天
        count+=2;

        for(var ci=0; ci<codes.length; ci++) {
            var code=codes[ci];
            
            //var stockid=ori_stockid.substr(1);	//通达信自定义板块会在代码前加0和1，分别代表深圳和上海
            //stockid='300505';
            var stock=this.loadStock(code);  //加载日线数据
            if( !Array.isArray(stock) || stock.length==0 ) continue;
            //检查日线除权除息方面是否有问题
            //{0 name, 1 vol, 2 amo, 3 c, 4 h, 5 l, 6 o, 7 prec, sz, ltsz}//价格单位是分，成交额与市值单位是百万
            const lasttd=this.gettd( stock[stock.length-2].split(','));
            const std=this.allStocks[code];
            if(std[1]==0 || std[3]==0) continue;

            if(  Math.round(lasttd.close*100)!=std[7]){
                await this.downloadKLine(code);  //重新下载日线，并重新读取日线
                console.log('可能除权除息，re-download klines:'+code);
                stock=this.loadStock(code);  //加载日线数据
                if( !Array.isArray(stock) || stock.length==0 ) continue;
            }
            
            for(var k=(stockIndex.length-1); k>0 && k>=(stockIndex.length-count); k--){
                const tdIndex=stockIndex[k].split(',');
                const index=tdIndex[0]; //日期

                //个股是否包含当日 有可能停牌
                const tdi=this.findday(stock, index);
                if(tdi==-1) continue;
                const td=this.gettd( stock[tdi].split(','));
                const pred=this.gettd( stock[tdi-1].split(','));

                if( (!(index in this.alldays)) || this.alldays[index].ok==1){
                    this.alldays[index]={};
                    //this.alldays[index].day=td.day;
                    //this.alldays[index].tradevalue=0;	//用连板数、涨停数、开板率、跌停数、涨停股票次日表现来计算，
                    //this.alldays[index].lb=0;	//连板数
                    this.alldays[index].zt=0;	//涨停数量
                    this.alldays[index].kb=0;	//开板数量
                    //this.alldays[index].ztrate=0;	//开板率 kbcount/(kbcount+ztcount)
                    this.alldays[index].dt=0;	//跌停
                    this.alldays[index].dtkb=0;	//跌停开板数
                    
                    this.alldays[index].yzb=0;	//一字板
                    
                    //this.alldays[index].zxtc=0;	//主线题材赚钱效应，也即大资金。将成交额大于15亿的涨幅相加。
                    
                    //this.alldays[index].crcount=0;	//涨停+开板股数量
                    //this.alldays[index].zth=0;	//涨停股次日表现，最高涨幅
                    //this.alldays[index].ztc=0;	//涨停股次日平均收盘涨幅		
                    this.alldays[index].zts=[];	//所有涨停股
                    this.alldays[index].zt1=0;   //首板涨停数量

                    this.alldays[index].kbs=[];	//开板股
                    this.alldays[index].dts=[];	//跌停股
                    
                    this.alldays[index].qsups=[];	//趋势股，连涨长时间排序
                    this.alldays[index].qsdowns=[];	//趋势股，连跌最长时间排序

                    this.alldays[index].bdups=[];	//波段股，20交易日波段涨幅排序
                    this.alldays[index].bddowns=[];	//波段股，波段跌幅最大排序
                    
                    
                    this.alldays[index].kpups=[];	//最强势高开股
                    this.alldays[index].kpdowns=[];	//低开股

                    this.alldays[index].tas=[];	//两市成交额前20
                    
                    this.alldays[index].fzups=[];	//反转股，日内上涨最多前20
                    this.alldays[index].fzdowns=[];	//反转股，日内下跌最多前20
                    
                    this.alldays[index].closeups=[];	//日内上涨最多前20
                    this.alldays[index].closedowns=[];//日内下跌最多前20
                    
                    this.alldays[index].aths=0;  //alltimehigh 历史新高
                    this.alldays[index].atls=0;  //alltimelow 历史新低
                    

                    this.alldays[index].ok=0;					
                }
                                         
                ///////////////////////////////////////////////////////////////////
                //是否高开股。记录市场最强龙头高开幅度，以便得知市场是处于加速段，还是分歧段。选择3天内至少有一天涨停且成交额大于2亿的
                //2018-06-14，改为统计市场所有个股高开前列的，借以研究竞价高开模式是赚是亏，如果是盈利的，表面市场较好
                //2019-03-14 改为近5个交易日有过涨停的个股的高开幅度
                var zf=100*(td.open/pred.close-1);                
                //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                var gks={c:code,zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amo/100000000).toFixed(2))};
                var inserted=false;
                for(var li=0; li<this.alldays[index].kpups.length; li++){
                    //开盘涨幅越大越靠前
                    if( zf>this.alldays[index].kpups[li].zf ){
                        this.alldays[index].kpups.splice(li,0,gks);
                        inserted=true;									
                        break;
                    }	
                }
                if(!inserted && this.alldays[index].kpups.length<30){
                    this.alldays[index].kpups.push(gks);
                }

                ///////////////////////////////////////////////////////////////////
                //是否低开股。当出现大量大幅低开股时，说明市场风险很大
                 zf=100*(td.open/pred.close-1);							
                //保存低开股
                var dks={c:code,zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amo/100000000).toFixed(2))};
                inserted=false;
                for( li=0; li<this.alldays[index].kpdowns.length; li++){
                    //开盘跌幅越大越靠前
                    if( zf<this.alldays[index].kpdowns[li].zf ){
                        this.alldays[index].kpdowns.splice(li,0,dks);
                        inserted=true;
                        break;
                    }	
                }
                if(!inserted && this.alldays[index].kpdowns.length<30){
                    this.alldays[index].kpdowns.push(dks);
                }

            
                ///////////////////////////////////////////////////////////////////
                //反转上涨股。从最低点到收盘，涨幅大于9%以上的，按照顺序排列
                zf=100*(td.close/td.low-1);                
                //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                var fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amo/100000000).toFixed(2))};
                inserted=false;
                for( li=0; li<this.alldays[index].fzups.length; li++){
                    //开盘涨幅越大越靠前
                    if( zf>this.alldays[index].fzups[li].zf ){
                        this.alldays[index].fzups.splice(li,0,fzs);
                        inserted=true;									
                        break;
                    }	
                }
                if(!inserted && zf>0 && this.alldays[index].fzups.length<30){
                    this.alldays[index].fzups.push(fzs);
                }

                ///////////////////////////////////////////////////////////////////
                //反转下跌股。从最高点到收盘，跌幅大于9%以上的，按照顺序排列
                zf=100*(td.close/td.high-1);                
                //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amo/100000000).toFixed(2))};
                inserted=false;
                for( li=0; li<this.alldays[index].fzdowns.length; li++){
                    //开盘涨幅越大越靠前
                    if( zf<0 && zf<this.alldays[index].fzdowns[li].zf ){
                        this.alldays[index].fzdowns.splice(li,0,fzs);
                        inserted=true;									
                        break;
                    }	
                }
                if(!inserted && zf<0 && this.alldays[index].fzdowns.length<30){
                    this.alldays[index].fzdowns.push(fzs);
                }
                
                ///////////////////////////////////////////////////////////////////
                //收盘上涨股。按照顺序排列
                zf=100*(td.close/pred.close-1);			
                //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amo/100000000).toFixed(2))};
                inserted=false;
                for( li=0; li<this.alldays[index].closeups.length; li++){
                    //涨幅越大越靠前
                    if( zf>0 && zf>this.alldays[index].closeups[li].zf ){
                        this.alldays[index].closeups.splice(li,0,fzs);
                        inserted=true;									
                        break;
                    }	
                }
                if(!inserted && zf>0 && this.alldays[index].closeups.length<30){
                    this.alldays[index].closeups.push(fzs);
                }


                ///////////////////////////////////////////////////////////////////
                //收盘下跌股。按照顺序排列
                zf=100*(td.close/pred.close-1);		
                //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat((td.amo/100000000).toFixed(2))};
                inserted=false;
                for( li=0; li<this.alldays[index].closedowns.length; li++){
                    //开盘涨幅越大越靠前
                    if( zf<0 && zf<this.alldays[index].closedowns[li].zf ){
                        this.alldays[index].closedowns.splice(li,0,fzs);
                        inserted=true;									
                        break;
                    }	
                }
                if(!inserted && zf<0 && this.alldays[index].closedowns.length<30){
                    this.alldays[index].closedowns.push(fzs);
                }

                ///////////////////////////////////////////////////////////////////
                //是否涨停股
                if( this.isRiseLimit(code, td.close,pred.close) ){	//是否涨停
                    var lbcount=0;
                    var isyzb=(td.close==td.low);// && td.amo<50000000);	//首板是否无量一字板，成交额小于5000万
                    
                    for(var v=tdi; v>0; v--){
                        const td2=this.gettd( stock[v].split(','));
                        const pred2=this.gettd( stock[v-1].split(','));

                        if( this.isRiseLimit(code, td2.close,pred2.close) ){                                
                            lbcount++;
                        }else{
                            break;
                        }
                    }
                    
                    this.alldays[index].zt+=lbcount>0?1:0;
                    this.alldays[index].yzb+=isyzb?1:0;
                    
                    //涨停股:股票代码，连板数，成交额
                    var lts={c:code,lb:lbcount,v:parseFloat((td.amo/100000000).toFixed(2))};
                    if(lbcount>0){
                        inserted=false;
                        for(li=0; li<this.alldays[index].zts.length; li++){
                            //连板越高越靠前，同等板数，成交额越大越靠前
                            if( (lbcount>this.alldays[index].zts[li].lb) || (lbcount==this.alldays[index].zts[li].lb && lts.v>this.alldays[index].zts[li].v) ){
                                this.alldays[index].zts.splice(li,0,lts);
                                inserted=true;									
                                break;
                            }	
                        }
                                                    
                        if(!inserted && this.alldays[index].zts.length<30){
                            this.alldays[index].zts.push(lts);
                        }
                    }
                }


                ///////////////////////////////////////////////////////////////////
                //是否跌停股
                if( this.isFallLimit(code, td.close,pred.close) ){	//是否跌停                                            
                    this.alldays[index].dt++;
                }
                
                ///////////////////////////////////////////////////////////////////
                //是否开板股
                if( td.close<td.high && this.isRiseLimit(code, td.high,pred.close) ){	//是否涨停                                            
                    this.alldays[index].kb++;
                }
                                
                ///////////////////////////////////////////////////////////////////
                //最大成交额                        
                zf=100*(td.close/pred.close-1);		
                var cv=td.amo/100000000;
                //保存高开股，分别是代码，开盘涨幅，收盘涨幅，成交额
                fzs={c:code, zf:parseFloat(zf.toFixed(2)),  v:parseFloat(cv.toFixed(2))};
                inserted=false;
                for( li=0; li<this.alldays[index].tas.length; li++){
                    //开盘涨幅越大越靠前
                    if(  cv>this.alldays[index].tas[li].v ){
                        this.alldays[index].tas.splice(li,0,fzs);
                        inserted=true;									
                        break;
                    }	
                }
                if(!inserted && this.alldays[index].tas.length<30){
                    this.alldays[index].tas.push(fzs);
                }
                                
                this.alldays[index].kpups.splice(30);
                this.alldays[index].kpdowns.splice(30);
                this.alldays[index].fzups.splice(30);
                this.alldays[index].fzdowns.splice(30);
                this.alldays[index].closeups.splice(30);
                this.alldays[index].closedowns.splice(30);
                this.alldays[index].zts.splice(30);
                this.alldays[index].tas.splice(30);

            }//end for days
        }//end for codes
        

        for( let day in this.alldays){
            if( this.alldays[day].ok==0 ){
                this.alldays[day].ok=1;
            }
        }

        //将复盘结果写入文件，如果每天写入的文字过多，或者复盘的时间太长，可以压缩或者按年分割，压缩可能好一点
        saveObjectToFile(this.alldays, this.dbFolder+'/dailyreview.json' );
        
        const st2=new Date().getTime()-st1;
        console.log('复盘完成，耗时：'+st2);
        
    }
    
}

module.exports=new eastmoneyService();