const StockApiService = require('./stockApi.js');
const NotificationService = require('./notificationService.js');

class MarketMonitor {
  constructor() {
    this.stocks = [];
    this.refreshTimer = null;
    this.lastStockData = {};
    this.settings = this.loadSettings();
  }

  // 加载设置
  loadSettings() {
    const defaultSettings = {
      refreshInterval: 30,
      monitorBreak: true,
      monitorDecrease: true,
      monitorChange: true,
      enableNotification: true
    };
    const savedSettings = JSON.parse(localStorage.getItem('monitorSettings') || '{}');
    return { ...defaultSettings, ...savedSettings };
  }

  // 保存设置
  saveSettings() {
    localStorage.setItem('monitorSettings', JSON.stringify(this.settings));
  }

  // 加载股票列表
  loadStocks() {
    const savedStocks = localStorage.getItem('monitoredStocks');
    if (savedStocks) {
      this.stocks = JSON.parse(savedStocks);
    } else {
      this.stocks = ['600519']; // 默认添加贵州茅台
    }
    this.saveStocks();
  }

  // 保存股票列表
  saveStocks() {
    localStorage.setItem('monitoredStocks', JSON.stringify(this.stocks));
  }

  // 添加股票
  addStock(code) {
    if (!this.stocks.includes(code)) {
      this.stocks.push(code);
      this.saveStocks();
      this.fetchStockData(code);
    }
  }

  // 删除股票
  removeStock(code) {
    this.stocks = this.stocks.filter(s => s !== code);
    this.saveStocks();
  }

  // 检查股票状态
  async checkStockStatus(code, currentData) {
    const lastData = this.lastStockData[code];
    if (!lastData) {
      this.lastStockData[code] = currentData;
      return;
    }

    // 检查炸板
    if (this.settings.monitorBreak && lastData.f40 > 0 && currentData.f40 === 0) {
      await NotificationService.notifyStockBreak(code, currentData.f58);
    }

    // 检查封单减少
    if (this.settings.monitorDecrease && lastData.f40 === 0 && currentData.f40 === 0) {
      const lastBuyVolume = lastData.f20;
      const currentBuyVolume = currentData.f20;
      if (lastBuyVolume > 0 && currentBuyVolume > 0) {
        const decreasePercent = ((lastBuyVolume - currentBuyVolume) / lastBuyVolume * 100).toFixed(2);
        if (decreasePercent > 10) {
          await NotificationService.notifyBuyVolumeDecrease(code, currentData.f58, decreasePercent);
        }
      }
    }

    // 检查封单变化量
    if (this.settings.monitorChange && lastData.f40 === 0 && currentData.f40 === 0) {
      const lastBuyVolume = lastData.f20;
      const currentBuyVolume = currentData.f20;
      if (lastBuyVolume !== currentBuyVolume) {
        const changeVolume = currentBuyVolume - lastBuyVolume;
        await NotificationService.notifyBuyVolumeChange(code, currentData.f58, changeVolume);
      }
    }

    this.lastStockData[code] = currentData;
  }

  // 获取单个股票数据
  async fetchStockData(code) {
    const stockData = await StockApiService.getStockRealtime(code);
    if (!stockData) return null;

    await this.checkStockStatus(code, stockData);
    return stockData;
  }

  // 获取所有股票数据
  async fetchAllStocks() {
    const stockDataList = await StockApiService.getStockList(this.stocks);
    return stockDataList.filter(data => data !== null);
  }

  // 启动定时刷新
  startRefreshTimer() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    this.refreshTimer = setInterval(() => this.fetchAllStocks(), this.settings.refreshInterval * 1000);
  }

  // 停止定时刷新
  stopRefreshTimer() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // 更新设置
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
    this.startRefreshTimer();
  }
}

module.exports = MarketMonitor; 